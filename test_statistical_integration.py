#!/usr/bin/env python3
"""
Test script to verify statistical analysis integration in the main file.

This script tests:
1. Import availability of statistical analysis functions
2. Basic functionality of statistical analysis components
3. Integration with the main workflow
"""

import sys
import os
import numpy as np

def test_imports():
    """Test if statistical analysis functions can be imported."""
    print("🧪 Testing Statistical Analysis Imports...")
    
    try:
        # Test import from main file
        from load_multilas_EEI_XCOR_PLOT_Final import (
            plot_statistical_analysis, plot_distribution_histograms,
            plot_parameter_histograms, plot_well_comparison_boxplots,
            run_comprehensive_statistical_analysis
        )
        print("✅ Successfully imported statistical functions from main file")
        
        # Test import from plotting components
        from ui.plotting_components import plot_statistical_analysis as plot_stats_pc
        print("✅ Successfully imported from ui.plotting_components")
        
        # Test import from statistical analysis module
        from ui.statistical_analysis import get_statistical_analyzer
        analyzer = get_statistical_analyzer()
        print("✅ Successfully imported and created statistical analyzer")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {str(e)}")
        return False

def test_statistical_analyzer():
    """Test basic functionality of the statistical analyzer."""
    print("\n🧪 Testing Statistical Analyzer Functionality...")
    
    try:
        from ui.statistical_analysis import get_statistical_analyzer
        analyzer = get_statistical_analyzer()
        
        # Create sample data
        sample_data = np.random.normal(0.5, 0.2, 100)
        stats = analyzer.calculate_statistical_summary(sample_data, "Test Data")
        
        if 'mean' in stats and 'std' in stats:
            print("✅ Statistical summary calculation works")
            print(f"   Sample stats: mean={stats['mean']:.3f}, std={stats['std']:.3f}")
        else:
            print("❌ Statistical summary missing expected fields")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing statistical analyzer: {str(e)}")
        return False

def test_plotting_components_availability():
    """Test if plotting components have statistical analysis available."""
    print("\n🧪 Testing Plotting Components Statistical Analysis Availability...")
    
    try:
        from ui.plotting_components import STATISTICAL_ANALYSIS_AVAILABLE
        
        if STATISTICAL_ANALYSIS_AVAILABLE:
            print("✅ Statistical analysis is available in plotting components")
            return True
        else:
            print("❌ Statistical analysis is NOT available in plotting components")
            print("   This indicates an import issue with ui.statistical_analysis")
            return False
            
    except ImportError as e:
        print(f"❌ Could not import STATISTICAL_ANALYSIS_AVAILABLE: {str(e)}")
        return False

def create_sample_well_data():
    """Create sample well data for testing."""
    sample_wells_data = []
    sample_wells_results = []
    
    for i in range(3):
        well_name = f"Well_{i+1}"
        
        # Create sample data
        depth = np.linspace(1000 + i*100, 1200 + i*100, 50)
        target = np.random.normal(0.3 + i*0.1, 0.05, 50)
        normalized_eei = target + np.random.normal(0, 0.02, 50)
        
        well_data = {
            'well_name': well_name,
            'depth': depth,
            'target': target,
            'normalized_eei': normalized_eei,
            'angle': 30 + i*10,
            'vol_wetclay': np.random.uniform(0, 0.3, 50)
        }
        
        well_result = {
            'well_name': well_name,
            'optimum_angle': 30 + i*10,
            'max_correlation': 0.8 + i*0.05,
            'top_depth': 1000 + i*100,
            'bottom_depth': 1200 + i*100
        }
        
        sample_wells_data.append(well_data)
        sample_wells_results.append(well_result)
    
    return sample_wells_data, sample_wells_results

def test_statistical_analysis_functions():
    """Test the statistical analysis functions with sample data."""
    print("\n🧪 Testing Statistical Analysis Functions with Sample Data...")
    
    try:
        # Create sample data
        all_wells_data, all_wells_results = create_sample_well_data()
        
        # Test comprehensive statistical analysis
        from load_multilas_EEI_XCOR_PLOT_Final import run_comprehensive_statistical_analysis
        print("   Testing comprehensive statistical analysis...")
        run_comprehensive_statistical_analysis(all_wells_data, all_wells_results, 1)  # EEI
        print("✅ Comprehensive statistical analysis completed successfully")
        
        # Test individual functions
        from load_multilas_EEI_XCOR_PLOT_Final import (
            run_distribution_histograms, run_parameter_histograms, run_well_comparison_boxplots
        )
        
        print("   Testing distribution histograms...")
        run_distribution_histograms(all_wells_data, 1)
        print("✅ Distribution histograms completed successfully")
        
        print("   Testing parameter histograms...")
        run_parameter_histograms(all_wells_results, 1)
        print("✅ Parameter histograms completed successfully")
        
        print("   Testing well comparison box plots...")
        run_well_comparison_boxplots(all_wells_data, 1)
        print("✅ Well comparison box plots completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing statistical analysis functions: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("="*60)
    print("🔍 STATISTICAL ANALYSIS INTEGRATION VERIFICATION")
    print("="*60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Statistical Analyzer Tests", test_statistical_analyzer),
        ("Plotting Components Availability", test_plotting_components_availability),
        ("Statistical Analysis Functions", test_statistical_analysis_functions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Statistical analysis integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Statistical analysis integration needs attention.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
