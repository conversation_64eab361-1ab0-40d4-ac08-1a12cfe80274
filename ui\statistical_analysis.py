"""
Statistical Analysis Module for Multi-Well EEI/CPEI/PEIL Analysis

This module provides comprehensive statistical analysis capabilities for
multi-well analysis including histograms, distribution analysis, and
comparative statistics for EEI, CPEI, and PEIL calculations.

Author: Assistant
Date: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from typing import List, Dict, Any, Optional, Tuple
import logging

# Import utility functions from the existing codebase
from ui.helper_functions import safe_format_float

# Set up logging
logger = logging.getLogger(__name__)


class StatisticalAnalyzer:
    """
    Comprehensive statistical analysis for multi-well EEI/CPEI/PEIL data.
    """
    
    def __init__(self):
        """Initialize the statistical analyzer."""
        self.plot_settings = {
            'figure_size_histogram': (12, 8),
            'figure_size_comparison': (15, 10),
            'figure_size_summary': (14, 10),
            'histogram_bins': 30,
            'alpha': 0.7,
            'colors': ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray'],
        }

        # Enhanced color and marker combinations for multi-well visualization
        self.colorblind_friendly_colors = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ]
        self.marker_shapes = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h', 'H', '+', 'x']

    def _get_color_marker_combinations(self, num_wells: int) -> List[Tuple[str, str]]:
        """
        Get color and marker combinations for multi-well visualization.

        Args:
            num_wells: Number of wells to generate combinations for

        Returns:
            List of (color, marker) tuples
        """
        combinations = []
        for i in range(num_wells):
            color = self.colorblind_friendly_colors[i % len(self.colorblind_friendly_colors)]
            marker = self.marker_shapes[i % len(self.marker_shapes)]
            combinations.append((color, marker))
        return combinations

    def _resample_data_points(self, x_data: np.ndarray, y_data: np.ndarray,
                            sample_fraction: float = 0.3, min_points: int = 50) -> Tuple[np.ndarray, np.ndarray]:
        """
        Resample data points to reduce plot density while maintaining distribution characteristics.

        Args:
            x_data: X coordinate data
            y_data: Y coordinate data
            sample_fraction: Fraction of data points to keep (default: 0.3 for 30%)
            min_points: Minimum number of points to keep regardless of fraction

        Returns:
            Tuple of (resampled_x, resampled_y)
        """
        try:
            total_points = len(x_data)

            # Calculate target number of points
            target_points = max(min_points, int(total_points * sample_fraction))

            # If we already have fewer points than target, return all
            if total_points <= target_points:
                return x_data, y_data

            # Use random sampling to maintain distribution characteristics
            np.random.seed(42)  # For reproducible results
            indices = np.random.choice(total_points, size=target_points, replace=False)
            indices = np.sort(indices)  # Sort to maintain some order

            return x_data[indices], y_data[indices]

        except Exception as e:
            logger.debug(f"Error in data resampling, returning original data: {str(e)}")
            return x_data, y_data

    def _add_smart_annotations(self, ax, x_values: List[float], y_values: List[float],
                             labels: List[str], fontsize: int = 9, use_data_coords: bool = False) -> None:
        """
        Add text annotations with smart positioning to avoid overlap.

        Args:
            ax: Matplotlib axes object
            x_values: X coordinates for annotations (data coords if use_data_coords=True, else normalized 0-1)
            y_values: Y coordinates for annotations (data coords if use_data_coords=True, else normalized 0-1)
            labels: Text labels for annotations
            fontsize: Font size for annotations
            use_data_coords: If True, use data coordinates; if False, use axes fraction (0-1)
        """
        try:
            used_positions = []

            if use_data_coords:
                # For data coordinates, calculate offset based on data range
                x_range = ax.get_xlim()[1] - ax.get_xlim()[0]
                y_range = ax.get_ylim()[1] - ax.get_ylim()[0]
                offset_distance_x = x_range * 0.05  # 5% of data range
                offset_distance_y = y_range * 0.05
            else:
                # For axes fraction coordinates (0-1)
                offset_distance_x = offset_distance_y = 0.02

            for i, (x, y, label) in enumerate(zip(x_values, y_values, labels)):
                # Calculate offset to avoid overlap
                offset_x, offset_y = 0, 0
                attempts = 0
                max_attempts = 8

                while attempts < max_attempts:
                    # Try different offset directions
                    if attempts == 0:
                        offset_x, offset_y = offset_distance_x, offset_distance_y
                    elif attempts == 1:
                        offset_x, offset_y = -offset_distance_x, offset_distance_y
                    elif attempts == 2:
                        offset_x, offset_y = offset_distance_x, -offset_distance_y
                    elif attempts == 3:
                        offset_x, offset_y = -offset_distance_x, -offset_distance_y
                    elif attempts == 4:
                        offset_x, offset_y = offset_distance_x * 2, 0
                    elif attempts == 5:
                        offset_x, offset_y = -offset_distance_x * 2, 0
                    elif attempts == 6:
                        offset_x, offset_y = 0, offset_distance_y * 2
                    else:
                        offset_x, offset_y = 0, -offset_distance_y * 2

                    # Check if position is too close to existing annotations
                    pos_x, pos_y = x + offset_x, y + offset_y
                    too_close = False

                    min_distance_x = offset_distance_x if use_data_coords else 0.02
                    min_distance_y = offset_distance_y if use_data_coords else 0.02

                    for used_x, used_y in used_positions:
                        if abs(pos_x - used_x) < min_distance_x and abs(pos_y - used_y) < min_distance_y:
                            too_close = True
                            break

                    if not too_close:
                        break
                    attempts += 1

                # Add annotation - Fix Issue 1: Use appropriate coordinate system
                if use_data_coords:
                    ax.annotate(label, (x, y), xytext=(pos_x, pos_y),
                               textcoords='data', fontsize=fontsize,
                               bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8),
                               ha='center', va='center')
                else:
                    ax.annotate(label, (x, y), xytext=(pos_x, pos_y),
                               textcoords='axes fraction', fontsize=fontsize,
                               bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8),
                               ha='center', va='center')

                used_positions.append((pos_x, pos_y))

        except Exception as e:
            logger.debug(f"Could not add smart annotations: {str(e)}")

    def calculate_statistical_summary(self, data: np.ndarray, name: str = "Data") -> Dict[str, float]:
        """
        Calculate comprehensive statistical summary for a dataset.
        
        Args:
            data: Input data array
            name: Name of the dataset for logging
            
        Returns:
            Dictionary containing statistical measures
        """
        try:
            # Remove NaN and infinite values
            clean_data = data[np.isfinite(data)]
            
            if len(clean_data) == 0:
                logger.warning(f"No valid data points for {name}")
                return self._empty_stats_dict()
            
            stats_dict = {
                'count': len(clean_data),
                'mean': np.mean(clean_data),
                'median': np.median(clean_data),
                'std': np.std(clean_data),
                'min': np.min(clean_data),
                'max': np.max(clean_data),
                'range': np.max(clean_data) - np.min(clean_data),
                'q25': np.percentile(clean_data, 25),
                'q75': np.percentile(clean_data, 75),
                'q95': np.percentile(clean_data, 95),
                'q05': np.percentile(clean_data, 5),
                'iqr': np.percentile(clean_data, 75) - np.percentile(clean_data, 25),
                'skewness': stats.skew(clean_data),
                'kurtosis': stats.kurtosis(clean_data),
                'cv': np.std(clean_data) / np.mean(clean_data) if np.mean(clean_data) != 0 else np.nan
            }
            
            logger.debug(f"Calculated statistics for {name}: {len(clean_data)} valid points")
            return stats_dict
            
        except Exception as e:
            logger.error(f"Error calculating statistics for {name}: {str(e)}")
            return self._empty_stats_dict()
    
    def _empty_stats_dict(self) -> Dict[str, float]:
        """Return empty statistics dictionary with NaN values."""
        return {
            'count': 0, 'mean': np.nan, 'median': np.nan, 'std': np.nan,
            'min': np.nan, 'max': np.nan, 'range': np.nan,
            'q25': np.nan, 'q75': np.nan, 'q95': np.nan, 'q05': np.nan,
            'iqr': np.nan, 'skewness': np.nan, 'kurtosis': np.nan, 'cv': np.nan
        }
    
    def analyze_well_data_distributions(self, all_wells_data: List[Dict[str, Any]], 
                                      analysis_type: str) -> Dict[str, Any]:
        """
        Analyze distributions of calculated values across all wells.
        
        Args:
            all_wells_data: List of well data dictionaries
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            
        Returns:
            Dictionary containing distribution analysis results
        """
        try:
            logger.info(f"Starting distribution analysis for {analysis_type} across {len(all_wells_data)} wells")
            
            # Extract data for analysis
            well_names = []
            calculated_values = []
            target_values = []
            depth_ranges = []
            
            for well_data in all_wells_data:
                if (well_data.get('normalized_eei') is not None and 
                    well_data.get('target') is not None):
                    
                    well_names.append(well_data['well_name'])
                    calculated_values.extend(well_data['normalized_eei'])
                    target_values.extend(well_data['target'])
                    
                    if well_data.get('depth') is not None:
                        depth_range = np.max(well_data['depth']) - np.min(well_data['depth'])
                        depth_ranges.append(depth_range)
            
            # Convert to numpy arrays
            calculated_values = np.array(calculated_values)
            target_values = np.array(target_values)
            depth_ranges = np.array(depth_ranges) if depth_ranges else np.array([])
            
            # Calculate statistics
            calc_stats = self.calculate_statistical_summary(calculated_values, f"{analysis_type} Values")
            target_stats = self.calculate_statistical_summary(target_values, "Target Values")
            depth_stats = self.calculate_statistical_summary(depth_ranges, "Depth Ranges")
            
            # Calculate correlations between wells if multiple wells
            well_correlations = self._calculate_inter_well_correlations(all_wells_data)
            
            analysis_results = {
                'analysis_type': analysis_type,
                'well_count': len(well_names),
                'total_data_points': len(calculated_values),
                'calculated_values_stats': calc_stats,
                'target_values_stats': target_stats,
                'depth_ranges_stats': depth_stats,
                'well_correlations': well_correlations,
                'well_names': well_names,
                'raw_data': {
                    'calculated_values': calculated_values,
                    'target_values': target_values,
                    'depth_ranges': depth_ranges,
                    'wells': all_wells_data  # Add the original wells data for plotting
                }
            }
            
            logger.info(f"Distribution analysis complete for {analysis_type}")
            return analysis_results
            
        except Exception as e:
            logger.error(f"Error in distribution analysis: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_inter_well_correlations(self, all_wells_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate correlations between wells."""
        try:
            correlations = {}
            valid_wells = [w for w in all_wells_data if w.get('normalized_eei') is not None]
            
            if len(valid_wells) < 2:
                return correlations
            
            for i, well1 in enumerate(valid_wells):
                for j, well2 in enumerate(valid_wells[i+1:], i+1):
                    try:
                        # Ensure both wells have data
                        data1 = np.array(well1['normalized_eei'])
                        data2 = np.array(well2['normalized_eei'])
                        
                        # Find common length (minimum of both)
                        min_len = min(len(data1), len(data2))
                        if min_len > 10:  # Minimum points for meaningful correlation
                            corr = np.corrcoef(data1[:min_len], data2[:min_len])[0, 1]
                            if np.isfinite(corr):
                                pair_name = f"{well1['well_name']}_vs_{well2['well_name']}"
                                correlations[pair_name] = corr
                    except Exception as e:
                        logger.debug(f"Could not calculate correlation between wells: {str(e)}")
                        continue
            
            return correlations
            
        except Exception as e:
            logger.error(f"Error calculating inter-well correlations: {str(e)}")
            return {}
    
    def analyze_optimization_parameters(self, all_wells_results: List[Dict[str, Any]], 
                                      analysis_type: str) -> Dict[str, Any]:
        """
        Analyze the distribution of optimization parameters across wells.
        
        Args:
            all_wells_results: List of optimization results for each well
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            
        Returns:
            Dictionary containing parameter analysis results
        """
        try:
            logger.info(f"Analyzing optimization parameters for {analysis_type}")
            
            well_names = []
            correlations = []
            parameters = []
            
            for result in all_wells_results:
                if result.get('max_correlation') is not None:
                    well_names.append(result['well_name'])
                    correlations.append(result['max_correlation'])
                    
                    if analysis_type == 'EEI':
                        # For EEI, extract angle
                        angle = result.get('optimum_angle', 0)
                        parameters.append({'angle': angle})
                    else:
                        # For CPEI/PEIL, extract n and phi from string
                        angle_str = result.get('optimum_angle', '')
                        n_val, phi_val = self._parse_cpei_peil_parameters(angle_str)
                        parameters.append({'n': n_val, 'phi': phi_val})
            
            # Convert to numpy arrays
            correlations = np.array(correlations)
            
            # Calculate correlation statistics
            corr_stats = self.calculate_statistical_summary(correlations, "Correlations")
            
            # Calculate parameter statistics
            param_stats = {}
            if analysis_type == 'EEI':
                angles = [p['angle'] for p in parameters if p['angle'] is not None]
                if angles:
                    param_stats['angles'] = self.calculate_statistical_summary(np.array(angles), "Angles")
            else:
                n_values = [p['n'] for p in parameters if p['n'] is not None]
                phi_values = [p['phi'] for p in parameters if p['phi'] is not None]
                
                if n_values:
                    param_stats['n_values'] = self.calculate_statistical_summary(np.array(n_values), "N Values")
                if phi_values:
                    param_stats['phi_values'] = self.calculate_statistical_summary(np.array(phi_values), "Phi Values")
            
            results = {
                'analysis_type': analysis_type,
                'well_count': len(well_names),
                'correlation_stats': corr_stats,
                'parameter_stats': param_stats,
                'well_names': well_names,
                'raw_data': {
                    'correlations': correlations,
                    'parameters': parameters
                }
            }
            
            logger.info(f"Parameter analysis complete for {analysis_type}")
            return results
            
        except Exception as e:
            logger.error(f"Error in parameter analysis: {str(e)}")
            return {'error': str(e)}
    
    def _parse_cpei_peil_parameters(self, param_string: str) -> Tuple[Optional[float], Optional[float]]:
        """Parse n and phi values from CPEI/PEIL parameter string."""
        try:
            if not param_string or param_string == 'None':
                return None, None
            
            # Expected format: "n=1.2, phi=45°"
            n_val = None
            phi_val = None
            
            parts = param_string.split(',')
            for part in parts:
                part = part.strip()
                if part.startswith('n='):
                    n_val = float(part.split('=')[1])
                elif part.startswith('phi='):
                    phi_str = part.split('=')[1].replace('°', '')
                    phi_val = float(phi_str)
            
            return n_val, phi_val
            
        except Exception as e:
            logger.debug(f"Could not parse parameters from '{param_string}': {str(e)}")
            return None, None
    
    def plot_value_distributions(self, distribution_analysis: Dict[str, Any], 
                               show_statistics: bool = True) -> None:
        """
        Plot histograms of calculated values and target values.
        
        Args:
            distribution_analysis: Results from analyze_well_data_distributions
            show_statistics: Whether to show statistical annotations
        """
        try:
            if 'error' in distribution_analysis:
                logger.error(f"Cannot plot distributions due to error: {distribution_analysis['error']}")
                return
            
            analysis_type = distribution_analysis['analysis_type']
            raw_data = distribution_analysis['raw_data']
            num_wells = len([w for w in raw_data['wells'] if w.get('normalized_eei') is not None])
            
            # Adjust title based on number of wells
            title_suffix = f"Multi-Well Distribution Analysis ({num_wells} wells)" if num_wells > 1 else "Single Well Distribution Analysis"
            
            fig, axes = plt.subplots(2, 2, figsize=self.plot_settings['figure_size_comparison'])
            fig.suptitle(f'{analysis_type} {title_suffix}', fontsize=16, fontweight='bold')
            
            # Plot 1: Calculated values histogram
            ax1 = axes[0, 0]
            calc_values = raw_data['calculated_values']
            if len(calc_values) > 0:
                # Adjust bins for small datasets
                bins = min(self.plot_settings['histogram_bins'], max(5, len(calc_values) // 3))
                ax1.hist(calc_values, bins=bins, 
                        alpha=self.plot_settings['alpha'], color='blue', edgecolor='black')
                ax1.set_xlabel(f'{analysis_type} Values')
                ax1.set_ylabel('Frequency')
                ax1.set_title(f'{analysis_type} Value Distribution')
                ax1.grid(True, alpha=0.3)
                
                if show_statistics:
                    stats = distribution_analysis['calculated_values_stats']
                    self._add_statistics_text(ax1, stats, 'upper right')
            else:
                ax1.text(0.5, 0.5, 'No valid calculated values', 
                        transform=ax1.transAxes, ha='center', va='center')
                ax1.set_title(f'{analysis_type} Value Distribution (No Data)')
            
            # Plot 2: Target values histogram
            ax2 = axes[0, 1]
            target_values = raw_data['target_values']
            if len(target_values) > 0:
                # Adjust bins for small datasets
                bins = min(self.plot_settings['histogram_bins'], max(5, len(target_values) // 3))
                ax2.hist(target_values, bins=bins, 
                        alpha=self.plot_settings['alpha'], color='red', edgecolor='black')
                ax2.set_xlabel('Target Log Values')
                ax2.set_ylabel('Frequency')
                ax2.set_title('Target Log Distribution')
                ax2.grid(True, alpha=0.3)
                
                if show_statistics:
                    stats = distribution_analysis['target_values_stats']
                    self._add_statistics_text(ax2, stats, 'upper right')
            else:
                ax2.text(0.5, 0.5, 'No valid target values', 
                        transform=ax2.transAxes, ha='center', va='center')
                ax2.set_title('Target Log Distribution (No Data)')
            
            # Plot 3: Enhanced multi-well scatter plot of calculated vs target
            ax3 = axes[1, 0]
            if len(calc_values) > 0 and len(target_values) > 0:
                try:
                    # Get wells with valid data
                    valid_wells = [w for w in raw_data['wells'] if
                                 w.get('normalized_eei') is not None and w.get('target') is not None]

                    if num_wells > 1 and len(valid_wells) > 1:
                        # Multi-well enhanced scatter plot
                        color_marker_combinations = self._get_color_marker_combinations(len(valid_wells))
                        legend_elements = []

                        for i, well in enumerate(valid_wells):
                            well_calc = np.array(well['normalized_eei'])
                            well_target = np.array(well['target'])

                            # Filter valid data points
                            valid_mask = np.isfinite(well_calc) & np.isfinite(well_target)
                            if np.sum(valid_mask) > 0:
                                color, marker = color_marker_combinations[i]
                                well_name = well['well_name']

                                # Enhancement 1: Apply data point reduction (30% sampling)
                                valid_calc = well_calc[valid_mask]
                                valid_target = well_target[valid_mask]

                                # Resample data points to improve readability and performance
                                resampled_calc, resampled_target = self._resample_data_points(
                                    valid_calc, valid_target, sample_fraction=0.3, min_points=50)

                                # Plot well-specific data with reduced points
                                scatter = ax3.scatter(resampled_calc, resampled_target,
                                                    alpha=0.7, color=color, marker=marker, s=30,
                                                    label=f'{well_name} ({len(resampled_calc)} pts)',
                                                    edgecolors='black', linewidth=0.5)
                                legend_elements.append(scatter)

                        # Add legend outside plot area
                        if legend_elements:
                            ax3.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1),
                                     loc='upper left', fontsize=9)
                    else:
                        # Single well or fallback to original behavior
                        min_len = min(len(calc_values), len(target_values))
                        ax3.scatter(calc_values[:min_len], target_values[:min_len],
                                   alpha=0.6, color='purple', s=20)

                    ax3.set_xlabel(f'{analysis_type} Values')
                    ax3.set_ylabel('Target Log Values')
                    ax3.set_title(f'{analysis_type} vs Target Correlation')
                    ax3.grid(True, alpha=0.3)

                    # Add overall correlation coefficient
                    min_len = min(len(calc_values), len(target_values))
                    if min_len > 1:
                        try:
                            corr = np.corrcoef(calc_values[:min_len], target_values[:min_len])[0, 1]
                            if np.isfinite(corr):
                                ax3.text(0.05, 0.95, f'Overall r = {corr:.3f}',
                                       transform=ax3.transAxes, fontsize=10,
                                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
                        except Exception:
                            pass

                except Exception as e:
                    logger.debug(f"Error in enhanced scatter plot, falling back to simple version: {str(e)}")
                    # Fallback to original simple scatter plot
                    min_len = min(len(calc_values), len(target_values))
                    ax3.scatter(calc_values[:min_len], target_values[:min_len],
                               alpha=0.6, color='purple', s=20)
                    ax3.set_xlabel(f'{analysis_type} Values')
                    ax3.set_ylabel('Target Log Values')
                    ax3.set_title(f'{analysis_type} vs Target Correlation')
                    ax3.grid(True, alpha=0.3)
            else:
                ax3.text(0.5, 0.5, 'Insufficient data for scatter plot',
                        transform=ax3.transAxes, ha='center', va='center')
                ax3.set_title(f'{analysis_type} vs Target (No Data)')
            
            # Plot 4: Well comparison (if multiple wells) or data quality plot (if single well)
            ax4 = axes[1, 1]
            if num_wells > 1:
                # Multi-well comparison
                well_names = [w['well_name'] for w in raw_data['wells'] if w.get('normalized_eei') is not None]
                well_means = []
                for well in raw_data['wells']:
                    if well.get('normalized_eei') is not None:
                        data = np.array(well['normalized_eei'])
                        valid_data = data[np.isfinite(data)]
                        if len(valid_data) > 0:
                            well_means.append(np.mean(valid_data))
                        else:
                            well_means.append(np.nan)
                
                if well_means and not all(np.isnan(well_means)):
                    ax4.bar(range(len(well_names)), well_means, alpha=0.7, color='green')
                    ax4.set_xlabel('Wells')
                    ax4.set_ylabel(f'Mean {analysis_type}')
                    ax4.set_title('Well Comparison')
                    ax4.set_xticks(range(len(well_names)))
                    ax4.set_xticklabels(well_names, rotation=45, ha='right')
                    ax4.grid(True, alpha=0.3)
                else:
                    ax4.text(0.5, 0.5, 'No valid data for comparison', 
                            transform=ax4.transAxes, ha='center', va='center')
            else:
                # Single well data quality plot
                if len(calc_values) > 0:
                    # Plot data density along depth if available
                    well_data = raw_data['wells'][0] if raw_data['wells'] else None
                    if well_data and well_data.get('depth') is not None:
                        depth = np.array(well_data['depth'])
                        values = np.array(well_data['normalized_eei'])
                        valid_mask = np.isfinite(values) & np.isfinite(depth)
                        
                        if np.sum(valid_mask) > 0:
                            ax4.plot(values[valid_mask], depth[valid_mask], 'o-', alpha=0.7, markersize=3)
                            ax4.set_xlabel(f'{analysis_type} Values')
                            ax4.set_ylabel('Depth')
                            ax4.set_title('Data vs Depth')
                            ax4.invert_yaxis()
                            ax4.grid(True, alpha=0.3)
                        else:
                            ax4.text(0.5, 0.5, 'No valid depth data', 
                                    transform=ax4.transAxes, ha='center', va='center')
                    else:
                        ax4.text(0.5, 0.5, 'Depth data not available', 
                                transform=ax4.transAxes, ha='center', va='center')
                else:
                    ax4.text(0.5, 0.5, 'No data available', 
                            transform=ax4.transAxes, ha='center', va='center')
                ax4.set_title('Data Quality Check')
            
            plt.tight_layout()
            plt.show()
            
            logger.info(f"Distribution plots generated for {analysis_type} ({num_wells} wells)")
            
        except Exception as e:
            logger.error(f"Error plotting value distributions: {str(e)}")
            print(f"Warning: Could not generate distribution plots: {str(e)}")
            
            # Plot 4: Depth ranges if available
            ax4 = axes[1, 1]
            depth_ranges = raw_data['depth_ranges']
            if len(depth_ranges) > 0:
                ax4.hist(depth_ranges, bins=min(15, len(depth_ranges)), 
                        alpha=self.plot_settings['alpha'], color='orange', edgecolor='black')
                ax4.set_xlabel('Depth Range (units)')
                ax4.set_ylabel('Frequency')
                ax4.set_title('Analysis Depth Ranges')
                ax4.grid(True, alpha=0.3)
                
                if show_statistics:
                    stats = distribution_analysis['depth_ranges_stats']
                    self._add_statistics_text(ax4, stats, 'upper right')
            else:
                ax4.text(0.5, 0.5, 'No depth range data available', 
                        transform=ax4.transAxes, ha='center', va='center')
                ax4.set_title('Depth Ranges')
            
            plt.tight_layout()
            plt.show()
            
            logger.info(f"Plotted value distributions for {analysis_type}")
            
        except Exception as e:
            logger.error(f"Error plotting value distributions: {str(e)}")
    
    def plot_parameter_distributions(self, parameter_analysis: Dict[str, Any], 
                                   show_statistics: bool = True) -> None:
        """
        Plot histograms of optimization parameters.
        
        Args:
            parameter_analysis: Results from analyze_optimization_parameters
            show_statistics: Whether to show statistical annotations
        """
        try:
            if 'error' in parameter_analysis:
                logger.error(f"Cannot plot parameters due to error: {parameter_analysis['error']}")
                return
            
            analysis_type = parameter_analysis['analysis_type']
            raw_data = parameter_analysis['raw_data']
            num_wells = len([r for r in raw_data['parameters'] if r.get('well_name')])
            
            # Adjust title based on number of wells
            title_suffix = f"Parameter Analysis ({num_wells} wells)" if num_wells > 1 else "Single Well Parameter Analysis"
            
            if analysis_type == 'EEI':
                # Enhancement 2: Expand EEI layout from 1x3 to 2x2 to include angle vs correlation plot and combined correlation plot
                fig, axes = plt.subplots(2, 2, figsize=(18, 12))  # 2x2 grid for 4 subplots
                fig.suptitle(f'{analysis_type} {title_suffix}', fontsize=16, fontweight='bold')

                # Plot correlations as bar chart (Enhancement 2)
                ax1 = axes[0, 0]
                correlations = raw_data['correlations']

                # Enhancement 2: Fix well name extraction - use actual well names from parameter_analysis
                actual_well_names = parameter_analysis.get('well_names', [])

                if len(correlations) > 0:
                    try:
                        # Use actual well names from the parameter analysis
                        if len(actual_well_names) == len(correlations):
                            well_labels = actual_well_names
                            corr_values = correlations
                        else:
                            # Fallback: create generic well names if mismatch
                            well_labels = [f'Well_{i+1}' for i in range(len(correlations))]
                            corr_values = correlations
                            logger.debug(f"Well name count mismatch: {len(actual_well_names)} names vs {len(correlations)} correlations")

                        # Create bar chart
                        bars = ax1.bar(range(len(well_labels)), corr_values,
                                      alpha=self.plot_settings['alpha'], color='blue', edgecolor='black')
                        ax1.set_xlabel('Wells')
                        ax1.set_ylabel('Correlation Coefficient')
                        ax1.set_title('Correlation Distribution by Well')
                        ax1.set_xticks(range(len(well_labels)))
                        ax1.set_xticklabels(well_labels, rotation=45, ha='right')
                        ax1.grid(True, alpha=0.3, axis='y')
                        ax1.set_ylim(0, 1.0)  # Set appropriate range for correlation coefficients

                        # Add value labels on bars
                        for bar, value in zip(bars, corr_values):
                            height = bar.get_height()
                            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                                   f'{value:.3f}', ha='center', va='bottom', fontsize=8)

                    except Exception as e:
                        logger.debug(f"Error creating correlation bar chart, falling back to histogram: {str(e)}")
                        # Fallback to original histogram
                        bins = min(15, max(5, len(correlations) // 2)) if len(correlations) > 1 else 1
                        ax1.hist(correlations, bins=bins,
                                alpha=self.plot_settings['alpha'], color='blue', edgecolor='black')
                        ax1.set_xlabel('Correlation Coefficient')
                        ax1.set_ylabel('Frequency')
                        ax1.set_title('Correlation Distribution')
                        ax1.grid(True, alpha=0.3)

                    if show_statistics:
                        stats = parameter_analysis['correlation_stats']
                        self._add_statistics_text(ax1, stats, 'upper left')
                else:
                    ax1.text(0.5, 0.5, 'No correlation data available',
                            transform=ax1.transAxes, ha='center', va='center')
                    ax1.set_title('Correlation Distribution (No Data)')
                
                # Plot angles with optimized bin size (Enhancement 3)
                ax2 = axes[0, 1]
                angles = [p['angle'] for p in raw_data['parameters'] if p['angle'] is not None]
                if angles:
                    # Optimized bin size: 10-15 bins instead of 20, minimum 5
                    bins = min(15, max(5, len(angles) // 2)) if len(angles) > 1 else 1
                    ax2.hist(angles, bins=bins,
                            alpha=self.plot_settings['alpha'], color='red', edgecolor='black')
                    ax2.set_xlabel('Optimum Angle (degrees)')
                    ax2.set_ylabel('Frequency')
                    ax2.set_title('Optimum Angle Distribution')
                    ax2.grid(True, alpha=0.3)
                    
                    if show_statistics:
                        stats = parameter_analysis['parameter_stats'].get('angles', {})
                        self._add_statistics_text(ax2, stats, 'upper right')
                    
                    # For single well, add the specific angle value as text
                    if len(angles) == 1:
                        ax2.text(0.5, 0.8, f'Optimum Angle: {angles[0]:.1f}°', 
                                transform=ax2.transAxes, ha='center', va='center',
                                bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
                else:
                    ax2.text(0.5, 0.5, 'No angle data available',
                            transform=ax2.transAxes, ha='center', va='center')
                    ax2.set_title('Optimum Angle Distribution (No Data)')

                # Enhancement 2: Move Angle vs Correlation Parameter Space to position (1,0)
                ax3 = axes[1, 0]
                if angles and len(correlations) > 0 and len(angles) == len(correlations):
                    try:
                        # Get actual well names for annotations
                        actual_well_names = parameter_analysis.get('well_names', [])

                        if len(angles) == 1:
                            # Single point - make it more visible
                            ax3.scatter(angles[0], correlations[0], alpha=1.0, color='green', s=200, marker='*')

                            # Enhanced annotation for single point
                            well_name = actual_well_names[0] if len(actual_well_names) > 0 else 'Well_1'
                            annotation_text = f'{well_name}\nr={correlations[0]:.3f}'
                            ax3.text(angles[0], correlations[0], f'  {annotation_text}',
                                    ha='left', va='center', fontsize=10,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
                        else:
                            # Multiple points - enhanced scatter with annotations
                            ax3.scatter(angles, correlations, alpha=0.7, color='green', s=50)

                            # Add annotations for each point
                            annotation_labels = []
                            for i in range(len(angles)):
                                # Get well name - use actual well names if available
                                if i < len(actual_well_names):
                                    well_name = actual_well_names[i]
                                else:
                                    well_name = f'Well_{i+1}'

                                # Format annotation
                                annotation_labels.append(f'{well_name}\nr={correlations[i]:.3f}')

                            # Add smart annotations to avoid overlap
                            if annotation_labels:
                                self._add_smart_annotations(ax3, angles, correlations, annotation_labels,
                                                           fontsize=8, use_data_coords=True)

                        ax3.set_xlabel('Optimum Angle (degrees)')
                        ax3.set_ylabel('Correlation Coefficient')
                        ax3.set_title('Angle vs Correlation Parameter Space')
                        ax3.grid(True, alpha=0.3)
                        ax3.set_ylim(0, 1.0)  # Set appropriate range for correlation coefficients

                    except Exception as e:
                        logger.debug(f"Error adding annotations to angle vs correlation plot, using simple version: {str(e)}")
                        # Fallback to simple scatter plot
                        if len(angles) == 1:
                            ax3.scatter(angles[0], correlations[0], alpha=1.0, color='green', s=200, marker='*')
                            ax3.text(angles[0], correlations[0], f'  ({angles[0]:.1f}°, r={correlations[0]:.3f})',
                                    ha='left', va='center', fontsize=10)
                        else:
                            ax3.scatter(angles, correlations, alpha=0.7, color='green', s=50)

                        ax3.set_xlabel('Optimum Angle (degrees)')
                        ax3.set_ylabel('Correlation Coefficient')
                        ax3.set_title('Angle vs Correlation Parameter Space')
                        ax3.grid(True, alpha=0.3)
                        ax3.set_ylim(0, 1.0)
                else:
                    ax3.text(0.5, 0.5, 'Insufficient angle/correlation data',
                            transform=ax3.transAxes, ha='center', va='center')
                    ax3.set_title('Angle vs Correlation Parameter Space (No Data)')

                # New fourth subplot at position (1,1): Combined correlation coefficients for all wells
                ax4 = axes[1, 1]
                if len(correlations) > 0 and len(actual_well_names) > 0:
                    try:
                        # Ensure we have matching data
                        if len(actual_well_names) == len(correlations):
                            well_labels = actual_well_names
                            corr_values = correlations
                        else:
                            # Fallback: create generic well names if mismatch
                            well_labels = [f'Well_{i+1}' for i in range(len(correlations))]
                            corr_values = correlations
                            logger.debug(f"Well name count mismatch in combined plot: {len(actual_well_names)} names vs {len(correlations)} correlations")

                        # Create line plot with markers for correlation coefficients
                        x_positions = range(len(well_labels))
                        ax4.plot(x_positions, corr_values, 'o-', linewidth=2, markersize=8,
                                color='darkblue', alpha=0.8, label='Correlation Coefficients')

                        # Add annotations at each point showing (well_name, correlation_value)
                        for i, (well_name, corr_val) in enumerate(zip(well_labels, corr_values)):
                            annotation_text = f'({well_name}, {corr_val:.3f})'
                            # Position annotation above the point
                            ax4.annotate(annotation_text,
                                       xy=(i, corr_val),
                                       xytext=(0, 10),  # 10 points offset above
                                       textcoords='offset points',
                                       ha='center', va='bottom',
                                       fontsize=8,
                                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
                                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

                        ax4.set_xlabel('Wells')
                        ax4.set_ylabel('Correlation Coefficient')
                        ax4.set_title('Combined Correlation Coefficients for All Wells')
                        ax4.set_xticks(x_positions)
                        ax4.set_xticklabels(well_labels, rotation=45, ha='right')
                        ax4.grid(True, alpha=0.3)
                        ax4.set_ylim(0, 1.0)  # Set appropriate range for correlation coefficients
                        ax4.legend(loc='upper right')

                        # Add horizontal line at mean correlation for reference
                        mean_corr = np.mean(corr_values)
                        ax4.axhline(y=mean_corr, color='red', linestyle='--', alpha=0.7,
                                   label=f'Mean: {mean_corr:.3f}')
                        ax4.legend(loc='upper right')

                    except Exception as e:
                        logger.debug(f"Error creating combined correlation plot, using simple version: {str(e)}")
                        # Fallback to simple bar chart
                        ax4.bar(range(len(correlations)), correlations, alpha=0.7, color='darkblue')
                        ax4.set_xlabel('Wells')
                        ax4.set_ylabel('Correlation Coefficient')
                        ax4.set_title('Combined Correlation Coefficients for All Wells')
                        ax4.set_xticks(range(len(correlations)))
                        ax4.set_xticklabels([f'Well_{i+1}' for i in range(len(correlations))], rotation=45, ha='right')
                        ax4.grid(True, alpha=0.3, axis='y')
                        ax4.set_ylim(0, 1.0)
                else:
                    ax4.text(0.5, 0.5, 'No correlation data available for combined plot',
                            transform=ax4.transAxes, ha='center', va='center')
                    ax4.set_title('Combined Correlation Coefficients (No Data)')

            else:  # CPEI or PEIL
                fig, axes = plt.subplots(2, 2, figsize=self.plot_settings['figure_size_comparison'])
                fig.suptitle(f'{analysis_type} {title_suffix}', fontsize=16, fontweight='bold')
                
                # Plot correlations as bar chart (Enhancement 2)
                ax1 = axes[0, 0]
                correlations = raw_data['correlations']

                # Enhancement 2: Fix well name extraction - use actual well names from parameter_analysis
                actual_well_names = parameter_analysis.get('well_names', [])

                if len(correlations) > 0:
                    try:
                        # Use actual well names from the parameter analysis for CPEI/PEIL
                        if len(actual_well_names) == len(correlations):
                            well_labels = actual_well_names
                            corr_values = correlations
                        else:
                            # Fallback: create generic well names if mismatch
                            well_labels = [f'Well_{i+1}' for i in range(len(correlations))]
                            corr_values = correlations
                            logger.debug(f"CPEI/PEIL well name count mismatch: {len(actual_well_names)} names vs {len(correlations)} correlations")

                        # Create bar chart
                        bars = ax1.bar(range(len(well_labels)), corr_values,
                                      alpha=self.plot_settings['alpha'], color='blue', edgecolor='black')
                        ax1.set_xlabel('Wells')
                        ax1.set_ylabel('Correlation Coefficient')
                        ax1.set_title('Correlation Distribution by Well')
                        ax1.set_xticks(range(len(well_labels)))
                        ax1.set_xticklabels(well_labels, rotation=45, ha='right')
                        ax1.grid(True, alpha=0.3, axis='y')
                        ax1.set_ylim(0, 1.0)  # Set appropriate range for correlation coefficients

                        # Add value labels on bars
                        for bar, value in zip(bars, corr_values):
                            height = bar.get_height()
                            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                                   f'{value:.3f}', ha='center', va='bottom', fontsize=8)

                    except Exception as e:
                        logger.debug(f"Error creating CPEI/PEIL correlation bar chart, falling back to histogram: {str(e)}")
                        # Fallback to original histogram
                        bins = min(15, max(5, len(correlations) // 2)) if len(correlations) > 1 else 1
                        ax1.hist(correlations, bins=bins,
                                alpha=self.plot_settings['alpha'], color='blue', edgecolor='black')
                        ax1.set_xlabel('Correlation Coefficient')
                        ax1.set_ylabel('Frequency')
                        ax1.set_title('Correlation Distribution')
                        ax1.grid(True, alpha=0.3)

                    if show_statistics:
                        stats = parameter_analysis['correlation_stats']
                        self._add_statistics_text(ax1, stats, 'upper left')
                else:
                    ax1.text(0.5, 0.5, 'No correlation data',
                            transform=ax1.transAxes, ha='center', va='center')
                
                # Plot n values with optimized bin size (Enhancement 3)
                ax2 = axes[0, 1]
                n_values = [p['n'] for p in raw_data['parameters'] if p['n'] is not None]
                if n_values:
                    # Optimized bin size: 10-15 bins instead of 20, minimum 5
                    bins = min(15, max(5, len(n_values) // 2)) if len(n_values) > 1 else 1
                    ax2.hist(n_values, bins=bins,
                            alpha=self.plot_settings['alpha'], color='green', edgecolor='black')
                    ax2.set_xlabel('Optimal n')
                    ax2.set_ylabel('Frequency')
                    ax2.set_title('Optimal n Distribution')
                    ax2.grid(True, alpha=0.3)

                    if show_statistics:
                        stats = parameter_analysis['parameter_stats'].get('n_values', {})
                        self._add_statistics_text(ax2, stats, 'upper right')

                    # For single well, add the specific n value as text
                    if len(n_values) == 1:
                        ax2.text(0.5, 0.8, f'Optimal n: {n_values[0]:.2f}',
                                transform=ax2.transAxes, ha='center', va='center',
                                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
                else:
                    ax2.text(0.5, 0.5, 'No n data',
                            transform=ax2.transAxes, ha='center', va='center')

                # Plot phi values with optimized bin size (Enhancement 3)
                ax3 = axes[1, 0]
                phi_values = [p['phi'] for p in raw_data['parameters'] if p['phi'] is not None]
                if phi_values:
                    # Optimized bin size: 10-15 bins instead of 20, minimum 5
                    bins = min(15, max(5, len(phi_values) // 2)) if len(phi_values) > 1 else 1
                    ax3.hist(phi_values, bins=bins,
                            alpha=self.plot_settings['alpha'], color='orange', edgecolor='black')
                    ax3.set_xlabel('Optimal φ (degrees)')
                    ax3.set_ylabel('Frequency')
                    ax3.set_title('Optimal φ Distribution')
                    ax3.grid(True, alpha=0.3)

                    if show_statistics:
                        stats = parameter_analysis['parameter_stats'].get('phi_values', {})
                        self._add_statistics_text(ax3, stats, 'upper right')

                    # For single well, add the specific phi value as text
                    if len(phi_values) == 1:
                        ax3.text(0.5, 0.8, f'Optimal φ: {phi_values[0]:.2f}°',
                                transform=ax3.transAxes, ha='center', va='center',
                                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.7))
                else:
                    ax3.text(0.5, 0.5, 'No φ data',
                            transform=ax3.transAxes, ha='center', va='center')
                
                # Plot n vs phi scatter with enhanced annotations (Enhancement 4)
                ax4 = axes[1, 1]
                if n_values and phi_values and len(n_values) == len(phi_values):
                    try:
                        # Get correlation values for annotations
                        correlations_for_annotation = raw_data.get('correlations', [])

                        if len(n_values) == 1:
                            # Single point - make it more visible
                            ax4.scatter(phi_values[0], n_values[0], alpha=1.0, color='purple', s=200, marker='*')

                            # Enhanced annotation for single point
                            if correlations_for_annotation and len(correlations_for_annotation) > 0:
                                corr_text = f'r={correlations_for_annotation[0]:.3f}'
                            else:
                                corr_text = 'r=N/A'

                            # Enhancement 2: Use actual well names from parameter_analysis
                            actual_well_names = parameter_analysis.get('well_names', [])
                            if len(actual_well_names) > 0:
                                well_name = actual_well_names[0]
                            else:
                                well_name = 'Well_1'

                            annotation_text = f'{well_name}\n{corr_text}'
                            ax4.text(phi_values[0], n_values[0], f'  {annotation_text}',
                                    ha='left', va='center', fontsize=10,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
                        else:
                            # Multiple points - enhanced scatter with annotations
                            ax4.scatter(phi_values, n_values, alpha=0.7, color='purple', s=50)

                            # Add annotations for each point
                            annotation_labels = []
                            # Enhancement 2: Use actual well names from parameter_analysis
                            actual_well_names = parameter_analysis.get('well_names', [])

                            for i in range(len(n_values)):
                                # Get well name - use actual well names if available
                                if i < len(actual_well_names):
                                    well_name = actual_well_names[i]
                                else:
                                    well_name = f'Well_{i+1}'

                                # Get correlation value
                                corr_text = 'r=N/A'
                                if i < len(correlations_for_annotation):
                                    corr_text = f'r={correlations_for_annotation[i]:.3f}'

                                annotation_labels.append(f'{well_name}\n{corr_text}')

                            # Add smart annotations to avoid overlap - Fix Issue 1: Use data coordinates
                            if annotation_labels:
                                # Use actual data coordinates instead of normalized coordinates
                                self._add_smart_annotations(ax4, phi_values, n_values, annotation_labels,
                                                           fontsize=8, use_data_coords=True)

                        ax4.set_xlabel('Optimal φ (degrees)')
                        ax4.set_ylabel('Optimal n')
                        ax4.set_title('n vs φ Parameter Space')
                        ax4.grid(True, alpha=0.3)

                    except Exception as e:
                        logger.debug(f"Error adding annotations to n vs phi plot, using simple version: {str(e)}")
                        # Fallback to simple scatter plot
                        if len(n_values) == 1:
                            ax4.scatter(phi_values[0], n_values[0], alpha=1.0, color='purple', s=200, marker='*')
                            ax4.text(phi_values[0], n_values[0], f'  ({phi_values[0]:.2f}, {n_values[0]:.2f})',
                                    ha='left', va='center', fontsize=10)
                        else:
                            ax4.scatter(phi_values, n_values, alpha=0.7, color='purple', s=50)

                        ax4.set_xlabel('Optimal φ (degrees)')
                        ax4.set_ylabel('Optimal n')
                        ax4.set_title('n vs φ Parameter Space')
                        ax4.grid(True, alpha=0.3)
                else:
                    ax4.text(0.5, 0.5, 'Insufficient parameter data',
                            transform=ax4.transAxes, ha='center', va='center')
                    ax4.set_title('n vs φ Parameter Space (No Data)')
            
            plt.tight_layout()
            plt.show()
            
            logger.info(f"Parameter plots generated for {analysis_type} ({num_wells} wells)")
            
        except Exception as e:
            logger.error(f"Error plotting parameter distributions: {str(e)}")
            print(f"Warning: Could not generate parameter plots: {str(e)}")
    
    def _add_statistics_text(self, ax, stats: Dict[str, float], location: str = 'upper right') -> None:
        """Add statistics text box to a plot."""
        try:
            if not stats or stats.get('count', 0) == 0:
                return
            
            stats_text = f"Count: {stats.get('count', 0)}\n"
            stats_text += f"Mean: {safe_format_float(stats.get('mean'), 3, 'N/A')}\n"
            stats_text += f"Std: {safe_format_float(stats.get('std'), 3, 'N/A')}\n"
            stats_text += f"Median: {safe_format_float(stats.get('median'), 3, 'N/A')}\n"
            stats_text += f"IQR: {safe_format_float(stats.get('iqr'), 3, 'N/A')}"
            
            # Determine position
            if location == 'upper right':
                x, y = 0.95, 0.95
                ha, va = 'right', 'top'
            elif location == 'upper left':
                x, y = 0.05, 0.95
                ha, va = 'left', 'top'
            elif location == 'lower right':
                x, y = 0.95, 0.05
                ha, va = 'right', 'bottom'
            else:  # lower left
                x, y = 0.05, 0.05
                ha, va = 'left', 'bottom'
            
            ax.text(x, y, stats_text, transform=ax.transAxes, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
                   verticalalignment=va, horizontalalignment=ha, fontsize=8, fontfamily='monospace')
                   
        except Exception as e:
            logger.debug(f"Could not add statistics text: {str(e)}")
    
    def plot_well_comparison_boxplots(self, all_wells_data: List[Dict[str, Any]], 
                                    analysis_type: str, max_wells: int = 10) -> None:
        """
        Create box plots comparing calculated values across wells.
        
        Args:
            all_wells_data: List of well data dictionaries
            analysis_type: Type of analysis ('EEI', 'CPEI', 'PEIL')
            max_wells: Maximum number of wells to include in comparison
        """
        try:
            # Filter valid wells and limit to max_wells
            valid_wells = [w for w in all_wells_data if w.get('normalized_eei') is not None][:max_wells]
            num_wells = len(valid_wells)
            
            if num_wells == 0:
                logger.warning("No valid wells for box plot comparison")
                return
            
            # Adjust plot based on number of wells
            if num_wells == 1:
                # Single well - create a different visualization
                fig, axes = plt.subplots(1, 2, figsize=(12, 6))
                fig.suptitle(f'{analysis_type} Single Well Data Analysis', fontsize=16, fontweight='bold')
                
                well = valid_wells[0]
                well_name = well['well_name']
                
                # Plot 1: Box plot of calculated values
                ax1 = axes[0]
                calc_data = np.array(well['normalized_eei'])
                valid_calc = calc_data[np.isfinite(calc_data)]
                
                if len(valid_calc) > 0:
                    ax1.boxplot([valid_calc], labels=[well_name])
                    ax1.set_ylabel(f'{analysis_type} Values')
                    ax1.set_title(f'{analysis_type} Value Distribution')
                    ax1.grid(True, alpha=0.3)
                    
                    # Add statistics text
                    stats_text = f'Count: {len(valid_calc)}\n'
                    stats_text += f'Mean: {np.mean(valid_calc):.3f}\n'
                    stats_text += f'Median: {np.median(valid_calc):.3f}\n'
                    stats_text += f'Std: {np.std(valid_calc):.3f}'
                    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
                            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                else:
                    ax1.text(0.5, 0.5, 'No valid calculated data', 
                            transform=ax1.transAxes, ha='center', va='center')
                
                # Plot 2: Box plot of target values
                ax2 = axes[1]
                target_data = np.array(well['target'])
                valid_target = target_data[np.isfinite(target_data)]
                
                if len(valid_target) > 0:
                    ax2.boxplot([valid_target], labels=[well_name])
                    ax2.set_ylabel('Target Log Values')
                    ax2.set_title('Target Log Distribution')
                    ax2.grid(True, alpha=0.3)
                    
                    # Add statistics text
                    stats_text = f'Count: {len(valid_target)}\n'
                    stats_text += f'Mean: {np.mean(valid_target):.3f}\n'
                    stats_text += f'Median: {np.median(valid_target):.3f}\n'
                    stats_text += f'Std: {np.std(valid_target):.3f}'
                    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                else:
                    ax2.text(0.5, 0.5, 'No valid target data', 
                            transform=ax2.transAxes, ha='center', va='center')
                
                logger.info(f"Created single well analysis for {well_name} ({analysis_type})")
            
            else:
                # Multiple wells - create comparison box plots
                # Prepare data for box plots
                well_names = []
                well_data_lists = []
                
                for well in valid_wells:
                    well_names.append(well['well_name'])
                    data = np.array(well['normalized_eei'])
                    clean_data = data[np.isfinite(data)]
                    well_data_lists.append(clean_data)
                
                # Create box plot
                fig, ax = plt.subplots(figsize=(max(8, len(well_names) * 1.2), 8))
                
                box_plot = ax.boxplot(well_data_lists, labels=well_names, patch_artist=True)
                
                # Color the boxes
                colors = self.plot_settings['colors'][:len(well_names)]
                for patch, color in zip(box_plot['boxes'], colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)
                
                ax.set_xlabel('Wells')
                ax.set_ylabel(f'{analysis_type} Values')
                ax.set_title(f'{analysis_type} Value Distribution Comparison Across Wells ({num_wells} wells)')
                ax.grid(True, alpha=0.3)
                
                # Rotate x-axis labels if many wells
                if len(well_names) > 5:
                    plt.xticks(rotation=45, ha='right')
                
                logger.info(f"Created box plot comparison for {len(valid_wells)} wells ({analysis_type})")
            
            plt.tight_layout()
            plt.show()
            
        except Exception as e:
            logger.error(f"Error creating well comparison box plots: {str(e)}")
            print(f"Warning: Could not generate box plot comparison: {str(e)}")
    
    def generate_statistical_report(self, distribution_analysis: Dict[str, Any], 
                                  parameter_analysis: Dict[str, Any]) -> str:
        """
        Generate a comprehensive statistical report.
        
        Args:
            distribution_analysis: Results from analyze_well_data_distributions
            parameter_analysis: Results from analyze_optimization_parameters
            
        Returns:
            Formatted statistical report as string
        """
        try:
            analysis_type = distribution_analysis.get('analysis_type', 'Unknown')
            
            report = f"\n{'='*60}\n"
            report += f"STATISTICAL ANALYSIS REPORT - {analysis_type}\n"
            report += f"{'='*60}\n\n"
            
            # General information
            report += f"Analysis Type: {analysis_type}\n"
            report += f"Number of Wells: {distribution_analysis.get('well_count', 0)}\n"
            report += f"Total Data Points: {distribution_analysis.get('total_data_points', 0)}\n"
            report += f"Wells Analyzed: {', '.join(distribution_analysis.get('well_names', []))}\n\n"
            
            # Calculated values statistics
            report += f"{analysis_type} VALUES STATISTICS:\n"
            report += f"{'-'*40}\n"
            calc_stats = distribution_analysis.get('calculated_values_stats', {})
            report += self._format_stats_for_report(calc_stats)
            
            # Target values statistics
            report += f"\nTARGET LOG VALUES STATISTICS:\n"
            report += f"{'-'*40}\n"
            target_stats = distribution_analysis.get('target_values_stats', {})
            report += self._format_stats_for_report(target_stats)
            
            # Parameter statistics
            report += f"\nOPTIMIZATION PARAMETERS:\n"
            report += f"{'-'*40}\n"
            corr_stats = parameter_analysis.get('correlation_stats', {})
            report += f"Correlation Statistics:\n"
            report += self._format_stats_for_report(corr_stats, indent="  ")
            
            param_stats = parameter_analysis.get('parameter_stats', {})
            if analysis_type == 'EEI':
                if 'angles' in param_stats:
                    report += f"\nAngle Statistics:\n"
                    report += self._format_stats_for_report(param_stats['angles'], indent="  ")
            else:
                if 'n_values' in param_stats:
                    report += f"\nOptimal n Statistics:\n"
                    report += self._format_stats_for_report(param_stats['n_values'], indent="  ")
                if 'phi_values' in param_stats:
                    report += f"\nOptimal φ Statistics:\n"
                    report += self._format_stats_for_report(param_stats['phi_values'], indent="  ")
            
            # Inter-well correlations
            well_corrs = distribution_analysis.get('well_correlations', {})
            if well_corrs:
                report += f"\nINTER-WELL CORRELATIONS:\n"
                report += f"{'-'*40}\n"
                for pair, corr in well_corrs.items():
                    report += f"{pair}: {safe_format_float(corr, 4, 'N/A')}\n"
            
            report += f"\n{'='*60}\n"
            
            logger.info(f"Generated statistical report for {analysis_type}")
            return report
            
        except Exception as e:
            logger.error(f"Error generating statistical report: {str(e)}")
            return f"Error generating report: {str(e)}"
    
    def _format_stats_for_report(self, stats: Dict[str, float], indent: str = "") -> str:
        """Format statistics dictionary for report."""
        if not stats:
            return f"{indent}No statistics available\n"
        
        formatted = ""
        formatted += f"{indent}Count: {stats.get('count', 'N/A')}\n"
        formatted += f"{indent}Mean: {safe_format_float(stats.get('mean'), 4, 'N/A')}\n"
        formatted += f"{indent}Median: {safe_format_float(stats.get('median'), 4, 'N/A')}\n"
        formatted += f"{indent}Std Dev: {safe_format_float(stats.get('std'), 4, 'N/A')}\n"
        formatted += f"{indent}Min: {safe_format_float(stats.get('min'), 4, 'N/A')}\n"
        formatted += f"{indent}Max: {safe_format_float(stats.get('max'), 4, 'N/A')}\n"
        formatted += f"{indent}25th Percentile: {safe_format_float(stats.get('q25'), 4, 'N/A')}\n"
        formatted += f"{indent}75th Percentile: {safe_format_float(stats.get('q75'), 4, 'N/A')}\n"
        formatted += f"{indent}IQR: {safe_format_float(stats.get('iqr'), 4, 'N/A')}\n"
        formatted += f"{indent}Skewness: {safe_format_float(stats.get('skewness'), 4, 'N/A')}\n"
        formatted += f"{indent}Kurtosis: {safe_format_float(stats.get('kurtosis'), 4, 'N/A')}\n"
        formatted += f"{indent}Coeff. of Variation: {safe_format_float(stats.get('cv'), 4, 'N/A')}\n"
        
        return formatted


# Global instance for easy access
_statistical_analyzer_instance = None

def get_statistical_analyzer():
    """Get the global statistical analyzer instance."""
    global _statistical_analyzer_instance
    if _statistical_analyzer_instance is None:
        _statistical_analyzer_instance = StatisticalAnalyzer()
    return _statistical_analyzer_instance


# Convenience functions for backward compatibility
def analyze_multi_well_distributions(all_wells_data: List[Dict[str, Any]], analysis_type: str) -> Dict[str, Any]:
    """Analyze distributions across multiple wells."""
    analyzer = get_statistical_analyzer()
    return analyzer.analyze_well_data_distributions(all_wells_data, analysis_type)

def analyze_optimization_parameters(all_wells_results: List[Dict[str, Any]], analysis_type: str) -> Dict[str, Any]:
    """Analyze optimization parameters across multiple wells."""
    analyzer = get_statistical_analyzer()
    return analyzer.analyze_optimization_parameters(all_wells_results, analysis_type)

def plot_statistical_summary(all_wells_data: List[Dict[str, Any]], all_wells_results: List[Dict[str, Any]], 
                           analysis_type: str) -> None:
    """Plot comprehensive statistical summary."""
    analyzer = get_statistical_analyzer()
    
    # Analyze distributions and parameters
    dist_analysis = analyzer.analyze_well_data_distributions(all_wells_data, analysis_type)
    param_analysis = analyzer.analyze_optimization_parameters(all_wells_results, analysis_type)
    
    # Plot distributions
    analyzer.plot_value_distributions(dist_analysis)
    analyzer.plot_parameter_distributions(param_analysis)
    analyzer.plot_well_comparison_boxplots(all_wells_data, analysis_type)
    
    # Generate and print report
    report = analyzer.generate_statistical_report(dist_analysis, param_analysis)
    print(report)